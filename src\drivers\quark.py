import os
import requests
from utils import utils
from typing import Dict, Any, List, Optional
from ._base import BaseDriver
from utils.kv_cache import KVCache
from core.log_manager import LogManager
from core.config_manager import ConfigManager

logger = LogManager.get_logger(__name__)


class QuarkDriver(BaseDriver):
    """夸克网盘驱动类"""

    DRIVER_TYPE = "quark"
    DRIVER_NAME = "夸克网盘"

    DRIVER_CONFIG = {
        "cookie": {
            "type": "string",
            "required": True,
            "label": "Cookie",
            "tip": "从浏览器 F12 获取的 Cookie",
        },
        "strm_mode": {
            "type": "options",
            "options": {
                "proxy": "全代理",
                "fallback": "智能回退",
                "direct": "仅转码直链",
            },
            "required": True,
            "label": "直链模式",
            "tip": " - 全代理：流量全部通过 SmartStrm 代理<br>- 智能回退：优先使用转码直链，不兼容的格式回退到代理<br>- 仅转码直链：仅使用转码直链，不兼容则无法播放",
        },
    }

    DRIVER_TIPS = {
        "type": "info",
        "message": "使用夸克网盘 PC API 访问<br>由 SmartStrm 提供 STRM 解析，默认使用转码资源，支持直链。",
    }

    BASE_URL = "https://drive-pc.quark.cn"
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) quark-cloud-drive/3.19.0 Chrome/112.0.5615.165 Electron/******** Safari/537.36 Channel/pckk_other_ch"

    def __init__(self, config: Dict[str, Any]):
        """初始化夸克网盘驱动

        Args:
            cookie: 夸克网盘的 Cookie
            strm_mode: 直链模式 (proxy/fallback/direct)
        """
        self.name = None
        self.cookie = config["cookie"].strip()
        self.strm_mode = config.get("strm_mode", "direct")  # 默认为仅转码直链
        self.fid_cache = KVCache()
        self.fid_cache.set("/", "0", None)
        self.headers = {
            "cookie": self.cookie,
            "content-type": "application/json",
            "user-agent": self.USER_AGENT,
        }
        self.config_manager = ConfigManager

    def init_info(self) -> bool:
        """初始化用户信息"""
        user_info = self.get_user_info()
        if not user_info:
            return False
        config = self.config_manager.get_storage(self.name)
        config["user_info"] = user_info
        self.config_manager.update_storage(self.name, config)
        return True

    def _send_request(
        self, method: str, url: str, **kwargs
    ) -> Optional[requests.Response]:
        """发送请求到夸克网盘 API

        Args:
            method: 请求方法
            url: 请求地址
            **kwargs: 请求参数

        Returns:
            Optional[requests.Response]: 响应对象，如果请求失败则返回 None
        """
        try:
            if "headers" in kwargs:
                headers = kwargs["headers"]
                del kwargs["headers"]
            else:
                headers = self.headers
            response = requests.request(method, url, headers=headers, **kwargs)
            return response
        except Exception as e:
            logger.error(f"请求夸克网盘 API 失败: {e}")
            return None

    def _get_fid_by_path(self, path: str) -> str:
        """获取指定路径的文件夹 ID

        Args:
            path: 文件夹路径

        Returns:
            str: 文件夹 ID
        """
        if not (fid := self.fid_cache.get(path)):
            url = f"{self.BASE_URL}/1/clouddrive/file/info/path_list"
            querystring = {"pr": "ucpro", "fr": "pc"}
            payload = {"file_path": [path], "namespace": "0"}
            response = self._send_request("POST", url, json=payload, params=querystring)
            if response and response.status_code == 200:
                data = response.json()
                if data["code"] == 0 and data["data"]:
                    fid = data["data"][0]["fid"]
                    self.fid_cache.set(path, fid, 60)
        return fid

    def _get_fid_by_fdir(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """根据路径获取文件的 fid 并触发缓存整个父目录的 fid

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 文件的 fid
        """
        if not (fid := self.fid_cache.get(path)):
            file_name = os.path.basename(path)
            result = self.list_files(os.path.dirname(path))
            if not result["success"]:
                return ""
            files = result["data"]
            file_info = next((f for f in files if f["name"] == file_name), None)
            if not file_info:
                return ""
            fid = file_info["fid"]
        return fid

    def list_files(self, path: str = "/") -> Dict[str, Any]:
        """列出指定路径下的文件

        Args:
            path: 要列出的路径

        Returns:
            Dict[str, Any]:
                success: 是否成功
                data: List[Dict]: 文件信息列表，每个文件包含 name, isdir, path, size, modified, created 信息，其他信息可选
                message: 错误信息
        """
        try:
            pdir_fid = self._get_fid_by_path(path)
            if not pdir_fid:
                logger.error(f"获取文件夹 ID 失败: {path}")
                return {"success": False, "message": "获取文件夹 ID 失败"}
            url = f"{self.BASE_URL}/1/clouddrive/file/sort"
            querystring = {
                "pr": "ucpro",
                "fr": "pc",
                "uc_param_str": "",
                "pdir_fid": pdir_fid,
                "_page": 1,
                "_size": 200,
                "_fetch_total": 1,
                "_fetch_sub_dirs": 0,
                "_sort": "file_type:asc,updated_at:desc",
                "_fetch_full_path": 1,
            }
            response = self._send_request("GET", url, params=querystring)
            if not response or response.status_code != 200:
                return {"success": False, "message": "API请求失败"}

            data = response.json()
            if data["code"] != 0:
                return {"success": False, "message": data["message"]}

            files = []
            for f in data["data"]["list"]:
                # 缓存 fid ，减少后续请求
                f_path = os.path.join(path, f["file_name"]).replace("\\", "/")
                self.fid_cache.set(f_path, f["fid"], 60)
                files.append(
                    {
                        "name": f["file_name"],
                        "isdir": f["dir"],
                        "path": f_path,
                        "size": f["size"] if not f["dir"] else "",
                        "modified": f["updated_at"] / 1000,
                        "created": f["created_at"] / 1000,
                        # 可选参数
                        "type": f["file_type"],
                        "fid": f["fid"],
                    }
                )
            return {"success": True, "data": files}
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            utils.debug_print_exc()
            return {"success": False, "message": e}

    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            fid = self._get_fid_by_fdir(path)
            if not fid:
                return False

            url = f"{self.BASE_URL}/1/clouddrive/file/delete"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {
                "action_type": 2,
                "filelist": [fid],
                "exclude_fids": [],
            }
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                return False

            data = response.json()
            return data["code"] == 0
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        try:
            fid = self._get_fid_by_fdir(path)
            if not fid:
                return False

            url = f"{self.BASE_URL}/1/clouddrive/file/rename"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {"fid": fid, "file_name": new_name}
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                return False

            data = response.json()
            return data["code"] == 0
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取下载 URL

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 下载 URL
        """
        # 判断是否为视频文件
        video_extensions = [".mp4", ".mkv", ".avi", ".mov", ".3gp", ".flv"]
        is_video = any(path.lower().endswith(ext) for ext in video_extensions)
        if is_video:
            # 使用视频播放API获取直链
            try:
                fid = self._get_fid_by_fdir(path)
                print(fid, path)
                if not fid:
                    return ""

                url = f"{self.BASE_URL}/1/clouddrive/file/v2/play/project"
                querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
                payload = {
                    "fid": fid,
                    "resolutions": "low,normal,high,super,2k,4k",
                    "supports": "fmp4_av,m3u8,dolby_vision",
                }
                response = self._send_request(
                    "POST", url, json=payload, params=querystring
                )
                if response.status_code == 200:
                    logger.debug(f"视频转码直链: {response.json()}")
                    data = response.json()
                    if data["code"] == 0 and data["data"]:
                        return data["data"]["video_list"][0]["video_info"]["url"]
            except Exception as e:
                logger.error(f"获取视频转码直链失败: {e}")
        return ""

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL

        Args:
            path: 文件路径
            file_info: 文件信息
        """
        return f"SMARTSTRM_BASE/smartstrm/{self.name}/{path}".replace("//", "/")

    def should_proxy(self, path: str, file_info: Dict[str, Any] = {}) -> bool:
        """判断是否需要代理

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            bool: 是否需要代理
        """
        if self.strm_mode == "proxy":
            # 全代理模式
            return True
        elif self.strm_mode == "direct":
            # 仅转码直链模式
            return False
        elif self.strm_mode == "fallback":
            # 智能回退模式：检查直链是否包含 m3u8
            direct_url = self.get_download_url(path, file_info)
            logger.debug(f"直链 URL: {direct_url}")
            if direct_url and "m3u8" in direct_url.lower():
                return True
            return False
        return False

    def get_proxy_info(self, path: str, file_info: Dict[str, Any] = {}) -> Dict[str, Any]:
        """获取代理所需的信息（URL和headers）

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            Dict[str, Any]: 包含 url 和 headers 的字典，用于代理请求
        """
        return self._get_download_http(path, file_info)

    def _get_download_http(self, path: str, file_info: Dict[str, Any] = {}) -> dict:
        """获取下载 HTTP 请求

        Args:
            path: 文件路径
            file_info: 文件信息

        Returns:
            str: 下载 HTTP 请求
        """
        try:
            fid = self._get_fid_by_fdir(path)
            if not fid:
                return {}

            url = f"{self.BASE_URL}/1/clouddrive/file/download"
            querystring = {"pr": "ucpro", "fr": "pc", "uc_param_str": ""}
            payload = {"fids": [fid], "speedup_session": ""}
            response = self._send_request("POST", url, json=payload, params=querystring)
            if not response or response.status_code != 200:
                logger.error(f"获取下载 URL 失败: {response.text}")
                return {}

            data = response.json()
            if data["code"] == 0 and data["data"]:
                set_cookie = response.cookies.get_dict()
                cookie_str = "; ".join(
                    [f"{key}={value}" for key, value in set_cookie.items()]
                )
                return {
                    "url": data["data"][0]["download_url"],
                    "headers": {
                        "user-agent": self.USER_AGENT,
                        "cookie": f"{self.cookie}; {cookie_str}",
                    },
                }
            return {}
        except Exception as e:
            logger.error(f"获取下载 URL 失败: {e}")
            return {}

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            bytes: 文件二进制数据
        """
        try:
            download_info = self._get_download_http(path, file_info)
            if not download_info:
                logger.error(f"获取下载信息失败: {path}")
                return b""

            response = requests.get(
                download_info["url"], headers=download_info["headers"]
            )
            response.raise_for_status()
            return response.content
        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""

    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        # https://pan.quark.cn/account/info?fr=pc&platform=pc
        #
        VIP_MAP = {"NORMAL": "普通用户", "EXP_SVIP": "88VIP", "SUPER_VIP": "SVIP"}
        NO_FACE = "https://image.quark.cn/s/uae/g/3o/broccoli/resource/202306/0d16b010-0f13-11ee-a22d-496578f265e7.png"
        # 用户名、头像
        response = self._send_request(
            "GET", "https://pan.quark.cn/account/info?fr=pc&platform=pc"
        )
        info1 = response.json().get("data", {}) if response else {}
        # 等级、空间
        response = self._send_request(
            "GET",
            f"{self.BASE_URL}/1/clouddrive/member?pr=ucpro&fr=pc",
        )
        info2 = response.json().get("data", {}) if response else {}
        member_type = info2.get("member_type")
        # 合并用户信息
        user_info = {
            "user_name": info1.get("nickname", ""),
            "user_face": info1.get("avatarUri") or NO_FACE,
            "vip_level": VIP_MAP.get(member_type, member_type),
            "space_info": {
                "total": info2.get("total_capacity", 0),
                "remain": info2.get("total_capacity", 0) - info2.get("use_capacity", 0),
                "use": info2.get("use_capacity", 0),
            },
        }
        return user_info
