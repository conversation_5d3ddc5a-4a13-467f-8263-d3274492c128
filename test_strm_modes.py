#!/usr/bin/env python3
"""
测试 STRM 模式功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from drivers.quark import QuarkDriver

def test_strm_modes():
    """测试不同的 STRM 模式"""
    
    # 测试配置
    test_configs = [
        {"cookie": "test_cookie", "strm_mode": "proxy"},
        {"cookie": "test_cookie", "strm_mode": "direct"},
        {"cookie": "test_cookie", "strm_mode": "fallback"},
    ]
    
    test_path = "/test/video.mp4"
    test_file_info = {}
    
    for config in test_configs:
        print(f"\n测试模式: {config['strm_mode']}")
        
        # 创建驱动实例
        driver = QuarkDriver(config)
        driver.name = "test_storage"
        
        # 测试 should_proxy 方法
        should_proxy = driver.should_proxy(test_path, test_file_info)
        print(f"  should_proxy: {should_proxy}")
        
        # 测试 get_proxy_info 方法
        proxy_info = driver.get_proxy_info(test_path, test_file_info)
        print(f"  proxy_info keys: {list(proxy_info.keys())}")

def test_range_parsing():
    """测试 Range 头部解析"""
    sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
    
    # 模拟导入 parse_range_header 函数
    # 这里我们直接复制函数代码进行测试
    import re
    
    def parse_range_header(range_header, content_length):
        """解析 Range 头部"""
        if not range_header:
            return None
        
        # 解析 Range: bytes=start-end
        match = re.match(r'bytes=(\d*)-(\d*)', range_header)
        if not match:
            return None
        
        start_str, end_str = match.groups()
        
        # 计算实际的开始和结束位置
        if start_str:
            start = int(start_str)
        else:
            start = 0
        
        if end_str:
            end = int(end_str)
        else:
            end = content_length - 1
        
        # 确保范围有效
        if start >= content_length:
            return None
        
        if end >= content_length:
            end = content_length - 1
        
        if start > end:
            return None
        
        return start, end
    
    # 测试用例
    test_cases = [
        ("bytes=0-499", 1000, (0, 499)),
        ("bytes=500-999", 1000, (500, 999)),
        ("bytes=500-", 1000, (500, 999)),
        ("bytes=-500", 1000, (0, 500)),
        ("bytes=0-", 1000, (0, 999)),
        ("bytes=1000-", 1000, None),  # 超出范围
        ("invalid", 1000, None),  # 无效格式
    ]
    
    print("\n测试 Range 头部解析:")
    for range_header, content_length, expected in test_cases:
        result = parse_range_header(range_header, content_length)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {range_header} -> {result} (期望: {expected})")

if __name__ == "__main__":
    print("开始测试 STRM 模式功能...")
    
    try:
        test_strm_modes()
        test_range_parsing()
        print("\n✓ 所有测试完成")
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
