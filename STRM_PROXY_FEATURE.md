# STRM 代理功能实现文档

## 功能概述

实现了当用户请求 `@app.route("/smartstrm/<storage_name>/<path:file_path>")` 时，quark驱动根据 `strm_mode` 来决定代理模式的功能。

## 实现的功能

### 1. 三种代理模式

- **proxy（全代理）**: `_get_download_http` 获取下载链接和cookie，由驱动向服务器请求文件，作为反向代理，再返回给浏览器用户
- **direct（仅转码直链）**: `get_download_url` 取直链，以302跳转，让用户直连
- **fallback（智能回退）**: 介于两者之间，当 `get_download_url` 链接含有 m3u8 字符，就代理，否则为302

### 2. 流式传输和断点续传

- 支持 HTTP Range 请求
- 实现了 206 Partial Content 响应
- 支持视频文件的断点续传
- 流式传输，性能优化

### 3. 统一接口设计

- 在 `BaseDriver` 基类中添加了 `should_proxy()` 和 `get_proxy_info()` 方法
- 其他驱动默认返回 False（不代理），保持兼容性
- 只有 QuarkDriver 实现了具体的代理逻辑

## 代码修改详情

### 1. QuarkDriver 类修改

**文件**: `src/drivers/quark.py`

- 在 `__init__` 方法中添加了 `strm_mode` 配置读取
- 实现了 `should_proxy()` 方法来判断是否需要代理
- 实现了 `get_proxy_info()` 方法来获取代理所需的信息

### 2. BaseDriver 基类修改

**文件**: `src/drivers/_base.py`

- 添加了 `should_proxy()` 抽象方法
- 添加了 `get_proxy_info()` 抽象方法
- 为其他驱动提供了默认实现

### 3. 其他驱动兼容性

**文件**: `src/drivers/open115.py`, `src/drivers/openlist.py`, `src/drivers/webdav.py`, `src/drivers/local.py`

- 为所有驱动添加了默认的 `should_proxy()` 和 `get_proxy_info()` 实现
- 默认返回 False（不代理），保持向后兼容

### 4. 路由处理逻辑修改

**文件**: `src/app.py`

- 修改了 `/smartstrm/<storage_name>/<path:file_path>` 路由
- 添加了 `parse_range_header()` 函数来解析 Range 头部
- 添加了 `handle_proxy_stream()` 函数来处理代理流式传输
- 支持 HTTP Range 请求和断点续传

## 使用方法

### 1. 配置 QuarkDriver

在存储配置中设置 `strm_mode`:

```json
{
  "cookie": "your_cookie_here",
  "strm_mode": "fallback"  // 可选: "proxy", "direct", "fallback"
}
```

### 2. 代理模式说明

- **proxy**: 所有请求都通过 SmartStrm 代理
- **direct**: 所有请求都直接302跳转到原始链接
- **fallback**: 智能判断，m3u8 格式使用代理，其他格式使用直链

### 3. 断点续传支持

客户端发送 Range 头部时，服务器会返回 206 Partial Content 响应：

```http
Range: bytes=0-1023
```

服务器响应：

```http
HTTP/1.1 206 Partial Content
Content-Range: bytes 0-1023/2048
Content-Length: 1024
```

## 性能特性

1. **流式传输**: 使用 8KB 块大小进行流式传输，减少内存占用
2. **断点续传**: 支持 HTTP Range 请求，允许客户端从任意位置开始下载
3. **智能回退**: fallback 模式可以根据文件类型自动选择最优传输方式
4. **错误处理**: 完善的错误处理和日志记录

## 测试

运行测试脚本验证功能：

```bash
python test_strm_modes.py
```

测试包括：
- 不同 strm_mode 的行为验证
- Range 头部解析功能测试
- 代理信息获取测试

## 注意事项

1. 代理模式会增加服务器的带宽和CPU使用
2. 对于大文件，建议使用 direct 或 fallback 模式
3. m3u8 格式的视频流必须使用代理模式才能正常播放
4. 确保网络连接稳定，避免代理传输中断

## 兼容性

- 与现有的其他驱动完全兼容
- 不影响现有的 STRM 文件生成逻辑
- 向后兼容，未配置 strm_mode 时默认使用 direct 模式
